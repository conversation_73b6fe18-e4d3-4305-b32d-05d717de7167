import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class FQPDPage extends StatefulWidget {
  final Question? question;

  const FQPDPage({
    super.key,
    this.question,
  });

  @override
  State<FQPDPage> createState() => _FQPDPageState();
}

class _FQPDPageState extends State<FQPDPage> {
  // Available question parts from the passed Question
  List<QuestionPart> get _availableQuestionParts =>
      widget.question?.questionParts ?? [];

  // List of selected question parts
  final List<QuestionPart> _selectedQuestionParts = [];

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: widget.question?.questionDescription ?? 'Store details',
      ),
      body: _availableQuestionParts.isEmpty
          ? _buildEmptyState(textTheme)
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Main dropdown section
                  _buildMainDropdown(textTheme),
                  const Gap(16),

                  // Selected items list
                  if (_selectedQuestionParts.isNotEmpty) ...[
                    ..._selectedQuestionParts.asMap().entries.map((entry) {
                      final index = entry.key;
                      final questionPart = entry.value;
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: _buildSelectedItemCard(
                            questionPart, index, textTheme),
                      );
                    }),
                  ],
                ],
              ),
            ),
    );
  }

  Widget _buildEmptyState(TextTheme textTheme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: AppColors.blackTint1,
            ),
            const Gap(16),
            Text(
              'No Question Parts Available',
              style: textTheme.montserratheadingmedium.copyWith(
                color: AppColors.blackTint1,
              ),
            ),
            const Gap(8),
            Text(
              'This question does not have any question parts to display.',
              style: textTheme.montserratParagraphSmall.copyWith(
                color: AppColors.blackTint1,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainDropdown(TextTheme textTheme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          // Add button (+)
          GestureDetector(
            onTap: _showAddItemBottomSheet,
            child: Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                color: AppColors.loginGreen,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.add,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          const Gap(12),

          // Dropdown content
          Expanded(
            child: Text(
              'Please Select',
              style: textTheme.montserratFormsField.copyWith(
                color: AppColors.blackTint1,
              ),
            ),
          ),

          // Dropdown arrow
          GestureDetector(
            onTap: _showAddItemBottomSheet,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.loginGreen,
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.keyboard_arrow_down,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedItemCard(
      QuestionPart questionPart, int index, TextTheme textTheme) {
    final hasImage = questionPart.itemImage != null &&
        questionPart.itemImage != '0' &&
        questionPart.itemImage!.isNotEmpty;

    return GestureDetector(
      onTap: () {
        // Navigate to QPMDPage with the current question
        context.router.push(QPMDRoute(
          question: widget.question,
          questionPart: questionPart,
        ));
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: AppColors.black10,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            // Remove button (-)
            GestureDetector(
              onTap: () => _removeSelectedItem(index),
              behavior: HitTestBehavior.opaque,
              child: Container(
                width: 32,
                height: 32,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.remove,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
            const Gap(12),

            // Content area
            Expanded(
              child: Row(
                children: [
                  // Text content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Progress indicator (like in the image)
                        Text(
                          '${index + 1}/5',
                          style: textTheme.montserratParagraphXsmall.copyWith(
                            color: AppColors.blackTint1,
                          ),
                        ),
                        const Gap(4),
                        Text(
                          questionPart.questionpartDescription ??
                              'Unnamed Item',
                          style: textTheme.montserratFormsField,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  // Image thumbnail (if available)
                  if (hasImage) ...[
                    const Gap(12),
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: AppColors.lightGrey1,
                        border: Border.all(color: AppColors.blackTint2),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          questionPart.itemImage!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: AppColors.lightGrey1,
                              child: const Icon(
                                Icons.image,
                                color: AppColors.blackTint1,
                                size: 24,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),

            const Gap(12),

            // Change selection dropdown arrow
            GestureDetector(
              onTap: () => _showChangeItemBottomSheet(index),
              behavior: HitTestBehavior.opaque,
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppColors.loginGreen,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddItemBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_availableQuestionParts.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select item',
                  style: Theme.of(context).textTheme.montserratheadingmedium,
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _availableQuestionParts.length,
                  itemBuilder: (context, index) {
                    final questionPart = _availableQuestionParts[index];
                    final isAlreadySelected = _selectedQuestionParts.any(
                        (item) =>
                            item.questionpartId == questionPart.questionpartId);

                    return InkWell(
                      onTap: isAlreadySelected
                          ? null
                          : () {
                              setState(() {
                                _selectedQuestionParts.add(questionPart);
                              });
                              Navigator.pop(context);
                            },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratFormsField
                                    .copyWith(
                                      color: isAlreadySelected
                                          ? AppColors.blackTint1
                                          : AppColors.black,
                                    ),
                              ),
                            ),
                            if (isAlreadySelected)
                              const Icon(
                                Icons.check,
                                color: AppColors.loginGreen,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _removeSelectedItem(int index) {
    setState(() {
      _selectedQuestionParts.removeAt(index);
    });
  }

  void _showChangeItemBottomSheet(int index) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_availableQuestionParts.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Change Selection',
                  style: Theme.of(context).textTheme.montserratheadingmedium,
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _availableQuestionParts.length,
                  itemBuilder: (context, itemIndex) {
                    final questionPart = _availableQuestionParts[itemIndex];
                    final currentItem = _selectedQuestionParts[index];
                    final isCurrentSelection = currentItem.questionpartId ==
                        questionPart.questionpartId;
                    final isAlreadySelected = _selectedQuestionParts.any(
                        (item) =>
                            item.questionpartId == questionPart.questionpartId);

                    return InkWell(
                      onTap: isCurrentSelection
                          ? null
                          : () {
                              setState(() {
                                _selectedQuestionParts[index] = questionPart;
                              });
                              Navigator.pop(context);
                            },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratFormsField
                                    .copyWith(
                                      color: (isAlreadySelected &&
                                              !isCurrentSelection)
                                          ? AppColors.blackTint1
                                          : AppColors.black,
                                    ),
                              ),
                            ),
                            if (isCurrentSelection)
                              const Icon(
                                Icons.radio_button_checked,
                                color: AppColors.primaryBlue,
                                size: 20,
                              )
                            else if (isAlreadySelected)
                              const Icon(
                                Icons.check,
                                color: AppColors.loginGreen,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
