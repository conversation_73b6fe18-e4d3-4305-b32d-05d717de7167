import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_widget.dart'
    show PhotoUploadWidget;

class DropdownWidget extends StatelessWidget {
  final Measurement measurement;
  final String? value;
  final Function(String?) onChanged;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final VoidCallback? onCameraTap;
  final bool isRequired;

  const DropdownWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final options = measurement.measurementOptions ?? [];

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: const [
          // BoxShadow(
          //   color: AppColors.black10,
          //   blurRadius: 4,
          //   offset: const Offset(0, 1),
          // ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with required indicator
          Row(
            children: [
              Expanded(
                child: Text(
                  measurement.measurementDescription ?? 'Select Option',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
            ],
          ),
          const Gap(16),
          DropdownButtonFormField<String>(
            value: value,
            decoration: InputDecoration(
              hintText: 'Select...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: AppColors.blackTint2),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: AppColors.blackTint2),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                // borderSide:
                //     const BorderSide(color: AppColors.primaryBlue, width: 2),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 8.0,
              ),
            ),
            isExpanded: true,
            items: options.map((option) {
              return DropdownMenuItem<String>(
                value: option.measurementOptionId?.toString(),
                child: Text(
                  option.measurementOptionDescription ?? 'Unnamed Option',
                  style: textTheme.bodyMedium?.copyWith(
                    color: AppColors.black,
                    fontSize: 15,
                  ),
                ),
              );
            }).toList(),
            onChanged: onChanged,
            validator: (selectedValue) {
              if (measurement.required == true && selectedValue == null) {
                return 'Please select an option';
              }
              return null;
            },
            icon: const Icon(
              Icons.keyboard_arrow_down,
              // color: AppColors.primaryBlue,
            ),
          ),
          // Camera section
          if (showCameraIcon) ...[
            const Gap(16),
            PhotoUploadWidget(
              onCameraPressed: () {
                if (onCameraTap != null) {
                  onCameraTap!();
                }
              },
            ),
            // Row(
            //   children: [
            //     // HI-RES Button
            //     Container(
            //       padding: const EdgeInsets.symmetric(
            //         horizontal: 12.0,
            //         vertical: 6.0,
            //       ),
            //       decoration: BoxDecoration(
            //         color: AppColors.lightGrey2,
            //         borderRadius: BorderRadius.circular(6),
            //         border: Border.all(
            //           color: AppColors.blackTint2,
            //           width: 1,
            //         ),
            //       ),
            //       child: Text(
            //         'HI-RES',
            //         style: textTheme.bodySmall?.copyWith(
            //           color: AppColors.blackTint1,
            //           fontSize: 11,
            //           fontWeight: FontWeight.w600,
            //         ),
            //       ),
            //     ),
            //     const Gap(12),
            //     // Camera Icon
            //     GestureDetector(
            //       onTap: onCameraTap,
            //       child: Container(
            //         width: 40,
            //         height: 40,
            //         decoration: BoxDecoration(
            //           color: isCameraMandatory ? Colors.amber : Colors.green,
            //           borderRadius: BorderRadius.circular(8),
            //         ),
            //         child: const Icon(
            //           Icons.camera_alt,
            //           color: Colors.white,
            //           size: 20,
            //         ),
            //       ),
            //     ),
            //   ],
            // ),
          ],
          if (measurement.required == true && value == null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                'This field is required',
                style: textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
